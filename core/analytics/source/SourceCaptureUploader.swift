//
//  SourceCaptureUploader.swift
//  Switcher-Pro
//
//  Created by AI Assistant on 29.08.25.
//  Copyright © 2025 Switcher Inc. All rights reserved.
//

import Foundation
import UIKit

// Protocol for common uploader interface
protocol SourceCaptureUploaderProtocol: AnyObject {
    var isUploading: Bool { get async }
    func start() async
    func cancel() async
}

protocol SourceCaptureUploaderDelegate: AnyObject {
    func sourceCaptureUploaderDidFinish(_ uploader: any SourceCaptureUploaderProtocol, result: Result<String, any Error>)
}

// The upload of the capture can be done in two modes:
// 1. Legacy mode: call API to get individual upload URL, then upload
// 2. Container mode: use shared container credentials to upload directly
//
// Filename format: {ordinal}_{sourceType}_{actionType}_{channelId}.jpg
// Example: 1_liveInput_activation_camera1.jpg
actor SourceCaptureUploader: AzureBlobUploaderDelegate, SourceCaptureUploaderProtocol {

    weak var delegate: (any SourceCaptureUploaderDelegate)?

    // MARK: - Upload properties

    private var _isUploading: Bool = false

    public var isUploading: Bool {
        get {
            return _isUploading
        }
    }

    private func setIsUploading(_ value: Bool) {
        _isUploading = value
    }

    // Uploader that uploads capture file in chunks if needed
    private var azureBlobUploader: AzureBlobUploader?

    // MARK: - URLs and metadata

    private let captureImage: UIImage
    private let broadcastId: String
    private let channelId: String
    private let sourceType: SourcePanelLogType
    private let isActivation: Bool
    private let ordinal: Int
    private let timestamp: Date

    // Container credentials for direct upload (new mode)
    private let containerCredentials: SourceCaptureUploadCoordinator.ContainerCredentials?

    private var firstStepUploadURL: URL {
        return SSNUserAccount.shared.apiUrl(withSuffix: "api/Telemetry/SourceLogUpload")
    }

    private var step1Output: Step1UploadCaptureOutput?
    private var localCaptureUrl: URL?

    // MARK: - Init

    init(captureImage: UIImage,
         broadcastId: String,
         channelId: String,
         sourceType: SourcePanelLogType,
         isActivation: Bool,
         ordinal: Int,
         timestamp: Date = Date(),
         delegate: (any SourceCaptureUploaderDelegate)? = nil) {
        self.captureImage = captureImage
        self.broadcastId = broadcastId
        self.channelId = channelId
        self.sourceType = sourceType
        self.isActivation = isActivation
        self.ordinal = ordinal
        self.timestamp = timestamp
        self.delegate = delegate
        self.containerCredentials = nil
    }

    // New init for container-based uploads
    init(captureImage: UIImage,
         broadcastId: String,
         channelId: String,
         sourceType: SourcePanelLogType,
         isActivation: Bool,
         ordinal: Int,
         timestamp: Date = Date(),
         containerCredentials: SourceCaptureUploadCoordinator.ContainerCredentials,
         delegate: (any SourceCaptureUploaderDelegate)? = nil) {
        self.captureImage = captureImage
        self.broadcastId = broadcastId
        self.channelId = channelId
        self.sourceType = sourceType
        self.isActivation = isActivation
        self.ordinal = ordinal
        self.timestamp = timestamp
        self.containerCredentials = containerCredentials
        self.delegate = delegate
    }

    // MARK: - Public methods

    func start() {
        Task {
            do {
                // Convert UIImage to JPEG data and save to temp file
                guard let imageData = captureImage.jpegData(compressionQuality: 0.8) else {
                    await end(result: .failure(SourceCaptureError.imageConversionFailed))
                    return
                }

                let tempDir = FileManager.default.temporaryDirectory
                let filename = createCaptureFileName()
                let tempUrl = tempDir.appendingPathComponent(filename)

                try imageData.write(to: tempUrl)
                localCaptureUrl = tempUrl

                // Choose upload mode based on available credentials
                if let containerCredentials = containerCredentials {
                    await uploadWithContainerCredentials(localFileUrl: tempUrl, filename: filename, credentials: containerCredentials)
                } else {
                    await uploadStep1(localFileUrl: tempUrl, filename: filename)
                }
            } catch {
                await end(result: .failure(error))
            }
        }
    }

    func cancel() {
        Task {
            await azureBlobUploader?.cancel()
            await end(result: .failure(SourceCaptureError.cancelled))
        }
    }

    // MARK: - Private methods

    private func createCaptureFileName() -> String {
        let actionType = isActivation ? "activation" : "deactivation"
        return "\(ordinal)_\(sourceType.id)_\(actionType)_\(channelId).jpg"
    }

    // MARK: - Container-based upload (new mode)

    private func uploadWithContainerCredentials(localFileUrl: URL, filename: String, credentials: SourceCaptureUploadCoordinator.ContainerCredentials) async {
        setIsUploading(true)

        do {
            // Create the blob URL using container credentials and folder structure
            guard let blobUrl = credentials.createBlobUrl(for: filename, broadcastId: broadcastId, timestamp: timestamp) else {
                await end(result: .failure(SourceCaptureError.invalidResponse))
                return
            }

            // Upload directly to the blob URL
            await uploadStep2(localFileUrl: localFileUrl, azureUploadUrl: blobUrl)

        } catch {
            await end(result: .failure(error))
        }
    }

    private func uploadStep1(localFileUrl: URL, filename: String) async {
        setIsUploading(true)

        do {
            let step1Input = Step1UploadCaptureInput(
                broadcastId: broadcastId,
                channelId: channelId,
                sourceType: sourceType.id,
                isActivation: isActivation,
                ordinal: ordinal,
                timestamp: timestamp,
                filename: filename
            )

            let step1Output = try await performStep1Upload(input: step1Input)
            self.step1Output = step1Output

            await uploadStep2(localFileUrl: localFileUrl, azureUploadUrl: step1Output.azureUploadUrl)

        } catch {
            await end(result: .failure(error))
        }
    }

    private func performStep1Upload(input: Step1UploadCaptureInput) async throws -> Step1UploadCaptureOutput {
        var request = URLRequest(url: firstStepUploadURL)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        // Add authorization header
        if let accessToken = SSNUserAccount.shared.accessToken {
            request.setValue("Bearer \(accessToken)", forHTTPHeaderField: "Authorization")
        }

        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        request.httpBody = try encoder.encode(input)

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw SourceCaptureError.invalidResponse
        }

        guard httpResponse.statusCode == 200 else {
            throw SourceCaptureError.httpError(httpResponse.statusCode)
        }

        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        return try decoder.decode(Step1UploadCaptureOutput.self, from: data)
    }

    private func uploadStep2(localFileUrl: URL, azureUploadUrl: URL) async {
        do {
            let uploader = AzureBlobUploader(
                serverFileURL: azureUploadUrl,
                localFilePathURL: localFileUrl,
                fileMimeType: "image/jpeg"
            )
            uploader.delegate = self
            azureBlobUploader = uploader
            uploader.start()
        } catch {
            await end(result: .failure(error))
        }
    }

    private func end(result: Result<String, any Error>) async {
        setIsUploading(false)

        // Clean up temp file
        if let localUrl = localCaptureUrl {
            try? FileManager.default.removeItem(at: localUrl)
        }

        await delegate?.sourceCaptureUploaderDidFinish(self, result: result)
    }

    // MARK: - AzureBlobUploaderDelegate

    nonisolated func assetBlobUploaderDidFinishChunkTransfer(index: Int, statusCode: Int, error: (any Error)?) {
        // Not needed for single file uploads
    }

    nonisolated func assetBlobUploaderProgressChunkTransfer(index: Int, percentage: Float) {
        // Not needed for single file uploads
    }

    nonisolated func assetBlobUploaderDidFinishBlobTransfer(success: Bool, error: (any Error)?) {
        Task {
            if success {
                if let step1Output = await step1Output {
                    // Legacy mode - use captureId from API response
                    await end(result: .success(step1Output.captureId))
                } else if await containerCredentials != nil {
                    // Container mode - generate a capture ID based on filename
                    let filename = await createCaptureFileName()
                    await end(result: .success(filename))
                } else {
                    await end(result: .failure(SourceCaptureError.missingStep1Output))
                }
            } else {
                await end(result: .failure(error ?? SourceCaptureError.cancelled))
            }
        }
    }

    nonisolated func assetBlobUploaderProgressBlobTransfer(progress: Float) {
        // Not needed for single file uploads
    }
}

// MARK: - Data Models

struct Step1UploadCaptureInput: Codable {
    let broadcastId: String
    let channelId: String
    let sourceType: String
    let isActivation: Bool
    let ordinal: Int
    let timestamp: Date
    let filename: String
}

struct Step1UploadCaptureOutput: Codable {
    let baseUrl: String
    let sasToken: String

    // Legacy support - computed property for backward compatibility
    var azureUploadUrl: URL? {
        guard let url = URL(string: baseUrl) else { return nil }
        return url.appendingPathComponent("dummy-file.jpg").appendingPathComponent("?\(sasToken)")
    }
}

// MARK: - Errors

enum SourceCaptureError: Error, LocalizedError {
    case imageConversionFailed
    case invalidResponse
    case httpError(Int)
    case cancelled
    case missingStep1Output
    case missingContainerCredentials

    var errorDescription: String? {
        switch self {
        case .imageConversionFailed:
            return "Failed to convert image to JPEG data"
        case .invalidResponse:
            return "Invalid response from server"
        case .httpError(let code):
            return "HTTP error: \(code)"
        case .cancelled:
            return "Upload was cancelled"
        case .missingStep1Output:
            return "Missing step 1 output data"
        case .missingContainerCredentials:
            return "Missing container credentials"
        }
    }
}