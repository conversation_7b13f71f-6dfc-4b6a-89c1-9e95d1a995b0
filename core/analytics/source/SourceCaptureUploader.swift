//
//  SourceCaptureUploader.swift
//  Switcher-Pro
//
//  Created by AI Assistant on 29.08.25.
//  Copyright © 2025 Switcher Inc. All rights reserved.
//

import Foundation
import UIKit

// Protocol for common uploader interface
protocol SourceCaptureUploaderProtocol: AnyObject {
    var isUploading: Bool { get async }
    func start() async
    func cancel() async
}

protocol SourceCaptureUploaderDelegate: AnyObject {
    func sourceCaptureUploaderDidFinish(_ uploader: any SourceCaptureUploaderProtocol, result: Result<String, any Error>)
}

// Upload captures using shared container credentials for optimal efficiency.
//
// Filename format: {ordinal}_{sourceType}_{actionType}_{channelId}.jpg
// Example: 1_liveInput_activation_camera1.jpg
actor SourceCaptureUploader: AzureBlobUploaderDelegate, SourceCaptureUploaderProtocol {

    weak var delegate: (any SourceCaptureUploaderDelegate)?

    // MARK: - Upload properties

    private var _isUploading: Bool = false

    public var isUploading: Bool {
        get {
            return _isUploading
        }
    }

    private func setIsUploading(_ value: Bool) {
        _isUploading = value
    }

    // Uploader that uploads capture file in chunks if needed
    private var azureBlobUploader: AzureBlobUploader?

    // MARK: - URLs and metadata

    private let captureImage: UIImage
    private let broadcastId: String
    private let channelId: String
    private let sourceType: SourcePanelLogType
    private let isActivation: Bool
    private let ordinal: Int
    private let timestamp: Date

    // Container credentials for direct upload
    private let containerCredentials: SourceCaptureUploadCoordinator.ContainerCredentials
    private var localCaptureUrl: URL?

    // MARK: - Init

    init(captureImage: UIImage,
         broadcastId: String,
         channelId: String,
         sourceType: SourcePanelLogType,
         isActivation: Bool,
         ordinal: Int,
         timestamp: Date = Date(),
         containerCredentials: SourceCaptureUploadCoordinator.ContainerCredentials,
         delegate: (any SourceCaptureUploaderDelegate)? = nil) {
        self.captureImage = captureImage
        self.broadcastId = broadcastId
        self.channelId = channelId
        self.sourceType = sourceType
        self.isActivation = isActivation
        self.ordinal = ordinal
        self.timestamp = timestamp
        self.containerCredentials = containerCredentials
        self.delegate = delegate
    }

    // MARK: - Public methods

    func start() {
        Task {
            do {
                // Convert UIImage to JPEG data and save to temp file
                guard let imageData = captureImage.jpegData(compressionQuality: 0.8) else {
                    await end(result: .failure(SourceCaptureError.imageConversionFailed))
                    return
                }

                let tempDir = FileManager.default.temporaryDirectory
                let filename = createCaptureFileName()
                let tempUrl = tempDir.appendingPathComponent(filename)

                try imageData.write(to: tempUrl)
                localCaptureUrl = tempUrl

                // Upload using container credentials
                await uploadWithContainerCredentials(localFileUrl: tempUrl, filename: filename, credentials: containerCredentials)
            } catch {
                await end(result: .failure(error))
            }
        }
    }

    func cancel() {
        Task {
            await azureBlobUploader?.cancel()
            await end(result: .failure(SourceCaptureError.cancelled))
        }
    }

    // MARK: - Private methods

    private func createCaptureFileName() -> String {
        let actionType = isActivation ? "activation" : "deactivation"
        return "\(ordinal)_\(sourceType.id)_\(actionType)_\(channelId).jpg"
    }

    // MARK: - Container-based upload

    private func uploadWithContainerCredentials(localFileUrl: URL, filename: String, credentials: SourceCaptureUploadCoordinator.ContainerCredentials) async {
        setIsUploading(true)

        do {
            // Create the blob URL using container credentials and folder structure
            guard let blobUrl = credentials.createBlobUrl(for: filename, broadcastId: broadcastId, timestamp: timestamp) else {
                await end(result: .failure(SourceCaptureError.invalidResponse))
                return
            }

            // Upload directly to the blob URL
            await uploadToAzure(localFileUrl: localFileUrl, azureUploadUrl: blobUrl)

        } catch {
            await end(result: .failure(error))
        }
    }

    private func uploadToAzure(localFileUrl: URL, azureUploadUrl: URL) async {
        do {
            let uploader = AzureBlobUploader(
                serverFileURL: azureUploadUrl,
                localFilePathURL: localFileUrl,
                fileMimeType: "image/jpeg"
            )
            uploader.delegate = self
            azureBlobUploader = uploader
            uploader.start()
        } catch {
            await end(result: .failure(error))
        }
    }

    private func end(result: Result<String, any Error>) async {
        setIsUploading(false)

        // Clean up temp file
        if let localUrl = localCaptureUrl {
            try? FileManager.default.removeItem(at: localUrl)
        }

        await delegate?.sourceCaptureUploaderDidFinish(self, result: result)
    }

    // MARK: - AzureBlobUploaderDelegate

    nonisolated func assetBlobUploaderDidFinishChunkTransfer(index: Int, statusCode: Int, error: (any Error)?) {
        // Not needed for single file uploads
    }

    nonisolated func assetBlobUploaderProgressChunkTransfer(index: Int, percentage: Float) {
        // Not needed for single file uploads
    }

    nonisolated func assetBlobUploaderDidFinishBlobTransfer(success: Bool, error: (any Error)?) {
        Task {
            if success {
                // Generate a capture ID based on filename
                let filename = await createCaptureFileName()
                await end(result: .success(filename))
            } else {
                await end(result: .failure(error ?? SourceCaptureError.cancelled))
            }
        }
    }

    nonisolated func assetBlobUploaderProgressBlobTransfer(progress: Float) {
        // Not needed for single file uploads
    }
}



// MARK: - Errors

enum SourceCaptureError: Error, LocalizedError {
    case imageConversionFailed
    case invalidResponse
    case cancelled
    case missingContainerCredentials

    var errorDescription: String? {
        switch self {
        case .imageConversionFailed:
            return "Failed to convert image to JPEG data"
        case .invalidResponse:
            return "Invalid response from server"
        case .cancelled:
            return "Upload was cancelled"
        case .missingContainerCredentials:
            return "Missing container credentials"
        }
    }
}